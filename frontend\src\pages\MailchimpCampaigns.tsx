import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, BarChart3, Mail, TrendingUp } from 'lucide-react'

const MailchimpCampaigns: React.FC = () => {
  const navigate = useNavigate()
  const [viewMode, setViewMode] = useState<'dashboard' | 'list' | 'analytics'>('dashboard')

  const handleCreateCampaign = () => {
    navigate('/campaigns/new')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Campaigns</h1>
              <p className="text-gray-600 mt-1">Create, manage, and track your email campaigns</p>
            </div>

            {/* Create Campaign Button */}
            <button
              onClick={handleCreateCampaign}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Campaign
            </button>
          </div>

          {/* Navigation Tabs */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
                { id: 'list', name: 'All Campaigns', icon: Mail },
                { id: 'analytics', name: 'Analytics', icon: TrendingUp }
              ].map(tab => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setViewMode(tab.id as any)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      viewMode === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 inline mr-2" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {viewMode === 'dashboard' && (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Campaign Dashboard</h2>
            <p className="text-gray-600 mb-8">Get started by creating your first campaign</p>
            <button
              onClick={handleCreateCampaign}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Your First Campaign
            </button>
          </div>
        )}

        {viewMode === 'list' && (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">All Campaigns</h2>
            <p className="text-gray-600 mb-8">No campaigns found. Create your first campaign to get started.</p>
            <button
              onClick={handleCreateCampaign}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Campaign
            </button>
          </div>
        )}

        {viewMode === 'analytics' && (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Campaign Analytics</h2>
            <p className="text-gray-600 mb-8">Analytics will appear here once you have sent campaigns.</p>
            <button
              onClick={handleCreateCampaign}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Campaign
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default MailchimpCampaigns